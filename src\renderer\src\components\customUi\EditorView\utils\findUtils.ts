import { Block, BlockNoteEditor } from '@blocknote/core';

export interface FindMatch {
  blockId: string;
  blockIndex: number;
  textIndex: number;
  length: number;
  text: string;
  blockText: string;
}

export interface FindResult {
  matches: FindMatch[];
  currentMatchIndex: number;
  totalMatches: number;
}

/**
 * Extract text content from a BlockNote block
 */
export function extractTextFromBlock(block: Block): string {
  try {
    console.log(`[findUtils] 📄 Extracting text from block: ${block.id}`);

    // Log block structure for debugging
    console.log(`[findUtils] 📋 Block type: ${block.type}`, block);

    let text = '';

    // Handle different block types with improved content extraction
    if (block.content && Array.isArray(block.content)) {
      text = block.content.map(item => {
        if (typeof item === 'string') {
          return item;
        }
        if (typeof item === 'object' && item !== null) {
          // Handle inline content objects
          if ('text' in item && typeof item.text === 'string') {
            return item.text;
          }
          // Handle other content types
          if ('type' in item && item.type === 'text' && 'text' in item) {
            return (item as any).text;
          }
        }
        return '';
      }).join('');
    }

    // Fallback: try to get text from the block's textContent if available
    if (!text && typeof (block as any).textContent === 'string') {
      text = (block as any).textContent;
    }

    console.log(`[findUtils] ✅ Extracted text (${text.length} chars): "${text.substring(0, 50)}${text.length > 50 ? '...' : ''}"`);
    return text;
  } catch (error) {
    console.error('[findUtils] 💥 Error extracting text from block:', error);
    return '';
  }
}

/**
 * Extract all text content from BlockNote document
 */
export function extractAllTextFromDocument(blocks: Block[]): string {
  return blocks
    .map(block => extractTextFromBlock(block))
    .join('\n');
}

/**
 * Find all matches of a search term in the document
 */
export function findInDocument(
  blocks: Block[],
  searchTerm: string,
  caseSensitive: boolean = false
): FindMatch[] {
  console.log(`[findUtils] 🔍 findInDocument called with: "${searchTerm}", caseSensitive: ${caseSensitive}`);
  console.log(`[findUtils] 📄 Processing ${blocks.length} blocks`);

  if (!searchTerm.trim()) {
    console.log('[findUtils] ⚠️ Empty search term, returning empty results');
    return [];
  }

  try {
    const matches: FindMatch[] = [];
    const term = caseSensitive ? searchTerm : searchTerm.toLowerCase();

    blocks.forEach((block, blockIndex) => {
      console.log(`[findUtils] 📑 Processing block ${blockIndex}/${blocks.length} (ID: ${block.id})`);

      // Extract text from the block
      const blockText = extractTextFromBlock(block);
      console.log(`[findUtils] 📝 Block text (${blockText.length} chars): "${blockText.substring(0, 50)}${blockText.length > 50 ? '...' : ''}"`);

      const textToSearch = caseSensitive ? blockText : blockText.toLowerCase();

      // Find all occurrences of the search term in this block
      let index = textToSearch.indexOf(term);
      let matchCount = 0;

      while (index !== -1) {
        matchCount++;
        matches.push({
          blockId: block.id,
          blockIndex: blockIndex,
          textIndex: index,
          length: term.length,
          text: blockText.substring(index, index + term.length), // Extract the actual matched text
          blockText: blockText
        });

        // Move to next occurrence
        index = textToSearch.indexOf(term, index + 1);
      }

      console.log(`[findUtils] ✅ Found ${matchCount} matches in this block`);
    });

    console.log(`[findUtils] 🎯 Total matches found: ${matches.length}`);
    return matches;
  } catch (error) {
    console.error('[findUtils] 💥 Error in findInDocument:', error);
    return [];
  }
}

/**
 * Navigate to a specific match in the editor
 */
export function navigateToMatch(
  editor: BlockNoteEditor,
  match: FindMatch
): void {
  try {
    // Find the block by ID
    const block = editor.document.find(b => b.id === match.blockId);
    if (!block) {
      console.warn('Block not found for match:', match.blockId);
      return;
    }

    // Set cursor position to the beginning of the match
    editor.setTextCursorPosition(block, "start");

    // Try to scroll the block into view
    const blockElement = document.querySelector(`[data-id="${match.blockId}"]`);
    if (blockElement) {
      blockElement.scrollIntoView({
        behavior: 'smooth',
        block: 'center'
      });
    }
  } catch (error) {
    console.error('Error navigating to match:', error);
  }
}

// Dynamic CSS management for find highlighting
let findStyleElement: HTMLStyleElement | null = null;

/**
 * Initialize or get the dynamic style element for find highlighting
 */
function getOrCreateFindStyleElement(): HTMLStyleElement {
  if (!findStyleElement) {
    findStyleElement = document.createElement('style');
    findStyleElement.id = 'find-highlight-styles';
    document.head.appendChild(findStyleElement);
    console.log('[findUtils] 🎨 Created dynamic style element for find highlighting');
  }
  return findStyleElement;
}

/**
 * Modern highlighting function using dynamic CSS instead of class manipulation
 */
export function highlightAllMatches(
  matches: FindMatch[],
  currentMatchIndex: number = -1
): void {
  try {
    console.log(`[findUtils] 🎨 Highlighting ${matches.length} matches, current: ${currentMatchIndex}`);

    // Clear all existing highlights first
    clearAllHighlights();

    if (matches.length === 0) {
      console.log('[findUtils] No matches to highlight');
      return;
    }

    // Get unique block IDs and count matches per block
    const blockMatchCounts = new Map<string, number>();
    matches.forEach(match => {
      const count = blockMatchCounts.get(match.blockId) || 0;
      blockMatchCounts.set(match.blockId, count + 1);
    });

    const currentBlockId = currentMatchIndex >= 0 ? matches[currentMatchIndex]?.blockId : null;

    console.log(`[findUtils] Highlighting ${blockMatchCounts.size} unique blocks using dynamic CSS`);

    // Generate CSS rules for each block
    const cssRules: string[] = [];

    blockMatchCounts.forEach((matchCount, blockId) => {
      const isCurrent = blockId === currentBlockId;

      if (isCurrent) {
        // Current match - stronger highlighting (target only inner .bn-block)
        cssRules.push(`
          .bn-block[data-id="${blockId}"] {
            border: 3px solid hsl(var(--primary)) !important;
            border-radius: 8px !important;
            background-color: hsl(var(--primary) / 0.1) !important;
            box-shadow: 0 0 0 1px hsl(var(--primary) / 0.3) !important;
            transition: all 0.2s ease-in-out !important;
            position: relative !important;
            padding: 10px;
          }
          .bn-block[data-id="${blockId}"]::before {
            content: "${matchCount}";
            position: absolute;
            top: -8px;
            left: -8px;
            background: hsl(var(--foreground));
            color: hsl(var(--background));
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 11px;
            font-weight: 600;
            z-index: 10;
            border: 2px solid hsl(var(--primary));
          }
        `);
        console.log(`[findUtils] ✅ Generated current match CSS for block ${blockId} with ${matchCount} matches`);
      } else {
        // Regular match - subtle highlighting (target only inner .bn-block)
        cssRules.push(`
          .bn-block[data-id="${blockId}"] {
            border: 2px solid hsl(var(--primary)) !important;
            border-radius: 8px !important;
            background-color: hsl(var(--primary) / 0.05) !important;
            transition: all 0.2s ease-in-out !important;
            position: relative !important;
            padding: 4px;
          }
          .bn-block[data-id="${blockId}"]::before {
            content: "${matchCount}";
            position: absolute;
            top: -8px;
            left: -8px;
            background: hsl(var(--foreground));
            color: hsl(var(--background));
            border-radius: 50%;
            width: 18px;
            height: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            font-weight: 600;
            z-index: 10;
          }
        `);
        console.log(`[findUtils] ✅ Generated regular match CSS for block ${blockId} with ${matchCount} matches`);
      }
    });

    // Apply the CSS rules
    const styleElement = getOrCreateFindStyleElement();
    styleElement.textContent = cssRules.join('\n');

    console.log(`[findUtils] 🎨 Applied ${cssRules.length} CSS rules for find highlighting with match counters`);
  } catch (error) {
    console.error('[findUtils] 💥 Error highlighting matches:', error);
  }
}

/**
 * Modern function to clear all highlights using dynamic CSS removal
 */
export function clearAllHighlights(): void {
  try {
    console.log('[findUtils] 🧹 Clearing all highlights');

    // Clear the dynamic CSS styles
    if (findStyleElement) {
      findStyleElement.textContent = '';
      console.log('[findUtils] ✅ Cleared dynamic CSS highlights');
    }

    // Also remove any legacy class-based highlights (fallback)
    const legacyHighlightedElements = document.querySelectorAll('.find-block-highlight, .find-block-highlight-current');
    if (legacyHighlightedElements.length > 0) {
      legacyHighlightedElements.forEach(element => {
        element.classList.remove('find-block-highlight', 'find-block-highlight-current');
      });
      console.log(`[findUtils] 🧹 Removed ${legacyHighlightedElements.length} legacy class-based highlights`);
    }

    console.log('[findUtils] ✅ Cleared all highlights');
  } catch (error) {
    console.error('[findUtils] 💥 Error clearing highlights:', error);
  }
}

/**
 * Remove highlights when text changes and no longer matches
 * Note: With dynamic CSS approach, this is less critical since highlights are regenerated on each search
 */
export function cleanupInvalidHighlights(
  searchTerm: string,
  _caseSensitive: boolean = false
): void {
  try {
    if (!searchTerm.trim()) {
      clearAllHighlights();
      return;
    }

    // With dynamic CSS approach, we can simply clear all highlights
    // The next search will regenerate the correct highlights
    console.log('[findUtils] 🧹 Cleaning up highlights - clearing all for regeneration');
    clearAllHighlights();
  } catch (error) {
    console.error('[findUtils] 💥 Error cleaning up invalid highlights:', error);
  }
}

/**
 * Create a find result object with improved index handling
 */
export function createFindResult(
  matches: FindMatch[],
  currentIndex: number = 0
): FindResult {
  const validCurrentIndex = matches.length > 0
    ? Math.max(0, Math.min(currentIndex, matches.length - 1))
    : -1;

  return {
    matches,
    currentMatchIndex: validCurrentIndex,
    totalMatches: matches.length
  };
}

/**
 * Get the next match index (wraps around)
 */
export function getNextMatchIndex(currentIndex: number, totalMatches: number): number {
  if (totalMatches === 0) return -1;
  return (currentIndex + 1) % totalMatches;
}

/**
 * Get the previous match index (wraps around)
 */
export function getPreviousMatchIndex(currentIndex: number, totalMatches: number): number {
  if (totalMatches === 0) return -1;
  return currentIndex <= 0 ? totalMatches - 1 : currentIndex - 1;
}

/**
 * Replace a specific match in the document using BlockNote's proper API
 */
export function replaceInDocument(
  editor: BlockNoteEditor,
  match: FindMatch,
  replacementText: string
): boolean {
  try {
    console.log('[findUtils] 🔄 Replacing match:', {
      blockId: match.blockId,
      textIndex: match.textIndex,
      originalText: match.text,
      replacementText
    });

    // Find the block by ID
    const block = editor.document.find(b => b.id === match.blockId);
    if (!block) {
      console.warn('[findUtils] ❌ Block not found for replacement:', match.blockId);
      return false;
    }

    // Extract the full text from the block
    const fullBlockText = extractTextFromBlock(block);
    console.log('[findUtils] 📄 Full block text:', fullBlockText);

    // Verify the match is still valid
    const matchText = fullBlockText.substring(match.textIndex, match.textIndex + match.length);
    if (matchText !== match.text) {
      console.warn('[findUtils] ❌ Match text has changed:', { expected: match.text, actual: matchText });
      return false;
    }

    // Create the new text with replacement
    const beforeMatch = fullBlockText.substring(0, match.textIndex);
    const afterMatch = fullBlockText.substring(match.textIndex + match.length);
    const newText = beforeMatch + replacementText + afterMatch;

    console.log('[findUtils] 🔄 Replacing text:', {
      before: beforeMatch,
      match: match.text,
      replacement: replacementText,
      after: afterMatch,
      newText
    });

    // Use a simpler approach: get the full text, replace it, and update the block
    // This handles complex content structures better

    console.log('[findUtils] 🔄 Updating block with new text:', {
      originalLength: fullBlockText.length,
      newLength: newText.length,
      replacement: replacementText
    });

    // Create new content with the replaced text
    const newContent = [{
      type: 'text',
      text: newText,
      styles: {}
    }];

    // Update the block with new content
    editor.updateBlock(block, {
      ...block,
      content: newContent
    } as any);

    console.log('[findUtils] ✅ Successfully replaced text in block:', match.blockId);
    return true;
  } catch (error) {
    console.error('[findUtils] 💥 Error replacing text in document:', error);
    return false;
  }
}

/**
 * Replace all matches in the document
 */
export function replaceAllInDocument(
  editor: BlockNoteEditor,
  matches: FindMatch[],
  replacementText: string
): number {
  try {
    console.log('[findUtils] 🔄 Replacing all matches:', matches.length);

    let replacedCount = 0;

    // Process matches one by one to ensure proper replacement
    // Sort by block and position to handle them in order
    const sortedMatches = [...matches].sort((a, b) => {
      if (a.blockId !== b.blockId) {
        return a.blockIndex - b.blockIndex;
      }
      return b.textIndex - a.textIndex; // Reverse order within block to avoid index shifting
    });

    // Group by block for batch processing
    const matchesByBlock = new Map<string, FindMatch[]>();
    sortedMatches.forEach(match => {
      if (!matchesByBlock.has(match.blockId)) {
        matchesByBlock.set(match.blockId, []);
      }
      matchesByBlock.get(match.blockId)!.push(match);
    });

    // Process each block
    matchesByBlock.forEach((blockMatches, blockId) => {
      const block = editor.document.find(b => b.id === blockId);
      if (!block || !block.content || !Array.isArray(block.content)) {
        console.warn('[findUtils] ❌ Block not found or has no content for replacement:', blockId);
        return;
      }

      console.log('[findUtils] 🔄 Processing block:', blockId, 'with', blockMatches.length, 'matches');

      // Get the full block text for validation
      const fullBlockText = extractTextFromBlock(block);
      let currentText = fullBlockText;

      // Apply replacements in reverse order to avoid index shifting
      const reversedMatches = [...blockMatches].sort((a, b) => b.textIndex - a.textIndex);

      reversedMatches.forEach(match => {
        // Validate the match is still at the expected position
        if (match.textIndex + match.length <= currentText.length &&
            currentText.substring(match.textIndex, match.textIndex + match.length) === match.text) {

          const beforeMatch = currentText.substring(0, match.textIndex);
          const afterMatch = currentText.substring(match.textIndex + match.length);
          currentText = beforeMatch + replacementText + afterMatch;
          replacedCount++;

          console.log('[findUtils] ✅ Replaced match in block:', blockId, 'at position:', match.textIndex);
        } else {
          console.warn('[findUtils] ❌ Match validation failed for block:', blockId, 'at position:', match.textIndex);
        }
      });

      // Update the block with the new text if it changed
      if (currentText !== fullBlockText) {
        const newContent = [{
          type: 'text',
          text: currentText,
          styles: {}
        }];

        editor.updateBlock(block, {
          ...block,
          content: newContent
        } as any);

        console.log('[findUtils] ✅ Updated block:', blockId);
      }
    });

    console.log('[findUtils] ✅ Replaced all matches:', replacedCount, 'out of', matches.length);
    return replacedCount;
  } catch (error) {
    console.error('[findUtils] 💥 Error replacing all matches in document:', error);
    return 0;
  }
}

/**
 * Debug function to analyze DOM structure for troubleshooting
 */
export function debugDOMStructure(): void {
  console.log('[findUtils] 🔍 DOM Structure Analysis:');

  // Check for BlockNote elements
  const bnElements = document.querySelectorAll('[class*="bn-"]');
  console.log(`Found ${bnElements.length} BlockNote elements`);

  // Check for data-id elements
  const dataIdElements = document.querySelectorAll('[data-id]');
  console.log(`Found ${dataIdElements.length} elements with data-id`);

  // Sample the first few elements
  const sampleElements = Array.from(dataIdElements).slice(0, 5);
  console.log('Sample elements:', sampleElements.map(el => ({
    id: el.getAttribute('data-id'),
    tagName: el.tagName,
    classes: Array.from(el.classList),
    textContent: el.textContent?.substring(0, 50) + '...'
  })));

  // Check for dynamic CSS highlighting
  const styleElement = document.getElementById('find-highlight-styles');
  if (styleElement) {
    console.log('Dynamic CSS style element found:', {
      hasContent: !!styleElement.textContent,
      contentLength: styleElement.textContent?.length || 0,
      contentPreview: styleElement.textContent?.substring(0, 200) + '...'
    });
  } else {
    console.log('No dynamic CSS style element found');
  }

  // Check for legacy class-based highlights
  const legacyHighlightedElements = document.querySelectorAll('.find-block-highlight, .find-block-highlight-current');
  console.log(`Found ${legacyHighlightedElements.length} legacy highlighted elements`);
}


