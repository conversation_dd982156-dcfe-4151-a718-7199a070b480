import { create } from 'zustand';
import { persist, createJSONStorage, devtools } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import { subscribeWithSelector } from 'zustand/middleware';
import { enableMapSet } from 'immer';
import { useCallback } from 'react';
import { FrontendStoredFile, DocumentSettings, ProjectSettings } from '@/types/global';
import fileSystemStorage from '@/lib/file-system-storage';
import { BlockNoteEditor } from '@blocknote/core';

// Enable Immer MapSet plugin for Map and Set support
enableMapSet();

// Block-related types
interface BlockState {
  content: any; // BlockNote Block type
  lastModified: string;
  isLoaded: boolean;
  isDirty: boolean;
  saveCount: number;
}

interface DocumentBlocksMetadata {
  version: string;
  documentId: string;
  blockOrder: string[];
  lastModified: string;
  totalBlocks: number;
}

interface BlockDocumentState {
  // Existing state from documentStore
  files: FrontendStoredFile[];
  openFiles: FrontendStoredFile[];
  activeFileId: string | null;
  detailedActiveFile: FrontendStoredFile | null;
  editorMode: 'edit' | 'preview' | 'split';
  projectSettings: ProjectSettings | null;
  currentStoragePath: string | null;

  // Editor instance for find functionality
  currentEditor: BlockNoteEditor | null;

  // Block-level state management
  blocks: Map<string, BlockState>; // blockId -> BlockState
  loadedDocuments: Set<string>; // documentIds that have been loaded
  dirtyBlocks: Set<string>; // blockIds that need saving
  documentsMetadata: Map<string, DocumentBlocksMetadata>; // docId -> metadata
  blockSaveQueue: Map<string, { content: any; operation: 'create' | 'update' | 'delete'; timestamp: number }>; // blockId -> operation
  isAutoSaving: boolean;
  lastAutoSaveTime: number | null;

  // Pending insertion content for saved paragraphs
  pendingInsertionContent: any[] | null;

  // Actions
  setActiveFileId: (id: string | null) => void;
  setDetailedActiveFile: (file: FrontendStoredFile | null) => void;
  setFiles: (files: FrontendStoredFile[]) => void;
  setEditorMode: (mode: 'edit' | 'preview' | 'split') => void;
  setProjectSettings: (settings: ProjectSettings | null) => void;
  setCurrentStoragePath: (path: string | null) => void;
  setCurrentEditor: (editor: BlockNoteEditor | null) => void;

  // Tab operations
  openTab: (file: FrontendStoredFile) => Promise<void>;
  closeTab: (fileId: string) => Promise<void>;
  selectTab: (fileId: string) => Promise<void>;
  reorderTabs: (newOrder: FrontendStoredFile[]) => void;

  // Legacy file operations (for backward compatibility)
  saveDocumentContent: (docId: string, content: string) => Promise<void>;
  saveDocumentMetadata: (docId: string, metadata: DocumentSettings) => Promise<void>;
  selectStorageDirectory: () => Promise<string | null>;
  unloadWorkspace: () => Promise<void>;

  // File operations
  refreshFiles: () => Promise<void>;
  loadFile: (id: string) => Promise<FrontendStoredFile | null>;
  createDocument: (name: string, parentId?: string | null) => Promise<FrontendStoredFile>;
  deleteFile: (id: string) => Promise<{ success: boolean; deletedIds: string[] }>;
  renameFile: (fileId: string, newName: string) => Promise<void>;
  duplicateFile: (fileIdToDuplicate: string, suggestedNewName: string) => Promise<FrontendStoredFile>;
  createFolder: (folderName: string, parentId?: string | null) => Promise<FrontendStoredFile>;
  exportFile: (fileToExport: FrontendStoredFile) => Promise<boolean>;
  moveItem: (itemId: string, newParentId: string | null) => Promise<{ movedItem: FrontendStoredFile; updatedChildren?: FrontendStoredFile[] }>;

  // Project settings
  loadProjectSettings: () => Promise<void>;
  saveProjectSettings: (settings: ProjectSettings) => Promise<void>;

  // Block-level operations
  loadDocumentBlocks: (documentId: string) => Promise<void>;
  updateBlock: (blockId: string, content: any) => void;
  updateActiveDocumentState: (docId: string, editorBlocks: any[], newBlockOrder: string[]) => void;
  createBlock: (blockId: string, content: any) => void;
  deleteBlock: (blockId: string) => void;
  markBlockClean: (blockId: string) => void;
  markBlockDirty: (blockId: string) => void;
  updateDocumentMetadata: (documentId: string, metadata: any) => void;
  getBlock: (blockId: string) => BlockState | null;
  getDocumentBlocks: (documentId: string) => BlockState[];
  getDirtyBlocks: () => string[];
  queueBlockForSave: (blockId: string, content: any, operation: 'create' | 'update' | 'delete') => void;
  clearSaveQueue: () => void;
  getSaveQueue: () => Map<string, { content: any; operation: 'create' | 'update' | 'delete'; timestamp: number }>;
  setAutoSaving: (saving: boolean) => void;
  updateLastAutoSaveTime: () => void;
  migrateDocumentToBlocks: (documentId: string) => Promise<void>;
  isDocumentBlockBased: (documentId: string) => Promise<boolean>;

  // Pending insertion content actions
  setPendingInsertionContent: (content: any[] | null) => void;
}

export const useDocumentStore = create<BlockDocumentState>()(
  devtools(
    persist(
      subscribeWithSelector(
        immer((set, get) => ({
      // Initial state
      files: [],
      openFiles: [],
      activeFileId: null,
      detailedActiveFile: null,
      editorMode: 'edit',
      projectSettings: null,
      currentStoragePath: null,
      currentEditor: null,
      blocks: new Map(),
      loadedDocuments: new Set(),
      dirtyBlocks: new Set(),
      documentsMetadata: new Map(),
      blockSaveQueue: new Map(),
      isAutoSaving: false,
      lastAutoSaveTime: null,
      pendingInsertionContent: null,

      // Basic setters
      setActiveFileId: (id: string | null) => set((state) => {
        state.activeFileId = id;
      }),

      setDetailedActiveFile: (file: FrontendStoredFile | null) => set((state) => {
        state.detailedActiveFile = file;
      }),

      setFiles: (files: FrontendStoredFile[]) => set((state) => {
        state.files = files;
      }),

      setEditorMode: (mode: 'edit' | 'preview' | 'split') => set((state) => {
        state.editorMode = mode;
      }),

      setProjectSettings: (settings: ProjectSettings | null) => set((state) => {
        state.projectSettings = settings;
      }),

      setCurrentStoragePath: (path: string | null) => set((state) => {
        state.currentStoragePath = path;
      }),

      setCurrentEditor: (editor: BlockNoteEditor | null) => set((state) => {
        state.currentEditor = editor;
      }),

      // Tab operations
      openTab: async (file: FrontendStoredFile) => {
        const { openFiles, setActiveFileId, loadFile, setDetailedActiveFile } = get();

        // Check if the file is already open
        const isOpen = openFiles.some(openFile => openFile.id === file.id);

        if (!isOpen) {
          set((state) => {
            state.openFiles.push(file);
          });
        }

        // Set as active file
        setActiveFileId(file.id);

        // Load the full file details including metadata
        const fileDetails = await loadFile(file.id);
        if (fileDetails) {
          setDetailedActiveFile(fileDetails);

          // Load blocks for the document if it's block-based
          const isBlockBased = await get().isDocumentBlockBased(file.id);
          console.log(`[Store] Document ${file.id} is block-based:`, isBlockBased);

          if (isBlockBased) {
            console.log(`[Store] Loading blocks for document ${file.id}`);
            await get().loadDocumentBlocks(file.id);

            // Check if blocks were loaded
            const loadedBlocks = get().getDocumentBlocks(file.id);
            console.log(`[Store] Loaded ${loadedBlocks.length} blocks for document ${file.id}`);

            // If no blocks were loaded, initialize with a default empty block
            if (loadedBlocks.length === 0) {
              console.log(`[Store] No blocks found for document ${file.id}, initializing with empty block`);
              const defaultBlockId = `${file.id}-block-0`;
              const defaultBlock = {
                id: defaultBlockId,
                type: 'paragraph',
                content: [],
                documentId: file.id
              };

              get().createBlock(defaultBlockId, defaultBlock);

              // Update metadata to include the new block
              const metadata = {
                version: '1.0',
                documentId: file.id,
                blockOrder: [defaultBlockId],
                lastModified: new Date().toISOString(),
                totalBlocks: 1
              };

              get().updateDocumentMetadata(file.id, metadata);
            }
          }
        } else {
          setDetailedActiveFile(file);
        }
      },

      closeTab: async (fileId: string) => {
        const { openFiles, activeFileId, setActiveFileId, setDetailedActiveFile, loadFile } = get();

        set((state) => {
          // Remove the file from openFiles
          state.openFiles = state.openFiles.filter(f => f.id !== fileId);
        });

        // If we're closing the active file, set active to the next available one or null
        if (activeFileId === fileId) {
          const updatedOpenFiles = get().openFiles;
          if (updatedOpenFiles.length > 0) {
            const nextActiveFile = updatedOpenFiles[0];
            setActiveFileId(nextActiveFile.id);
            const fileDetails = await loadFile(nextActiveFile.id);
            if (fileDetails) {
              setDetailedActiveFile(fileDetails);
            }
          } else {
            setActiveFileId(null);
            setDetailedActiveFile(null);
          }
        }
      },

      selectTab: async (fileId: string) => {
        const { setActiveFileId, loadFile, setDetailedActiveFile } = get();
        setActiveFileId(fileId);

        // Load the file details
        const fileDetails = await loadFile(fileId);
        if (fileDetails) {
          setDetailedActiveFile(fileDetails);

          // Load blocks for the document if it's block-based
          const isBlockBased = await get().isDocumentBlockBased(fileId);
          console.log(`[Store] Document ${fileId} is block-based:`, isBlockBased);

          if (isBlockBased) {
            console.log(`[Store] Loading blocks for document ${fileId}`);
            await get().loadDocumentBlocks(fileId);

            // Check if blocks were loaded
            const loadedBlocks = get().getDocumentBlocks(fileId);
            console.log(`[Store] Loaded ${loadedBlocks.length} blocks for document ${fileId}`);

            // If no blocks were loaded, initialize with a default empty block
            if (loadedBlocks.length === 0) {
              console.log(`[Store] No blocks found for document ${fileId}, initializing with empty block`);
              const defaultBlockId = `${fileId}-block-0`;
              const defaultBlock = {
                id: defaultBlockId,
                type: 'paragraph',
                content: [],
                documentId: fileId
              };

              get().createBlock(defaultBlockId, defaultBlock);

              // Update metadata to include the new block
              const metadata = {
                version: '1.0',
                documentId: fileId,
                blockOrder: [defaultBlockId],
                lastModified: new Date().toISOString(),
                totalBlocks: 1
              };

              get().updateDocumentMetadata(fileId, metadata);
            }
          }
        }
      },

      reorderTabs: (newOrder: FrontendStoredFile[]) => set((state) => {
        state.openFiles = newOrder;
      }),

      // Legacy file operations (for backward compatibility)
      saveDocumentContent: async (docId: string, content: string): Promise<void> => {
        const { loadFile, detailedActiveFile, setDetailedActiveFile } = get();
        try {
          await fileSystemStorage.saveDocumentContent(docId, content);
          // If the active file was saved, refresh its details
          if (detailedActiveFile && detailedActiveFile.id === docId) {
            const updatedFileDetails = await loadFile(docId);
            if (updatedFileDetails) {
              setDetailedActiveFile(updatedFileDetails);
            }
          }
        } catch (error) {
          console.error(`Error saving content for docId ${docId}:`, error);
          throw error;
        }
      },

      saveDocumentMetadata: async (docId: string, metadata: DocumentSettings): Promise<void> => {
        const { detailedActiveFile, loadFile, setDetailedActiveFile } = get();
        try {
          await fileSystemStorage.saveDocumentMetadata(docId, metadata);
          // Refresh the active file to get the latest metadata
          if (detailedActiveFile && detailedActiveFile.id === docId) {
            const updatedFile = await loadFile(docId);
            if (updatedFile) {
              setDetailedActiveFile(updatedFile);
            }
          }
        } catch (error) {
          console.error(`Error saving document metadata for ${docId}:`, error);
          throw error;
        }
      },

      selectStorageDirectory: async (): Promise<string | null> => {
        try {
          const newPath = await fileSystemStorage.selectDirectory();
          if (newPath) {
            set((state) => {
              state.currentStoragePath = newPath;
            });
            const { loadProjectSettings, refreshFiles } = get();
            await loadProjectSettings();
            await refreshFiles();
          }
          return newPath;
        } catch (error) {
          console.error('Error selecting storage directory:', error);
          throw error;
        }
      },

      unloadWorkspace: async (): Promise<void> => {
        try {
          // Clear storage path from main process
          await fileSystemStorage.clearStoragePath();

          // Reset all workspace-related state
          set((state) => {
            state.currentStoragePath = null;
            state.files = [];
            state.openFiles = [];
            state.activeFileId = null;
            state.detailedActiveFile = null;
            state.projectSettings = null;
            state.blocks.clear();
            state.loadedDocuments.clear();
            state.dirtyBlocks.clear();
            state.documentsMetadata.clear();
            state.blockSaveQueue.clear();
            state.isAutoSaving = false;
            state.lastAutoSaveTime = null;
          });
        } catch (error) {
          console.error('Error unloading workspace:', error);
          throw error;
        }
      },

      // File operations
      refreshFiles: async () => {
        try {
          const files = await fileSystemStorage.listFiles();
          set((state) => {
            state.files = files;
          });
        } catch (error) {
          console.error('Error refreshing files:', error);
          throw error;
        }
      },

      loadFile: async (id: string): Promise<FrontendStoredFile | null> => {
        try {
          return await fileSystemStorage.loadFile(id);
        } catch (error) {
          console.error(`Error loading file ${id}:`, error);
          throw error;
        }
      },

      createDocument: async (name: string, parentId?: string | null): Promise<FrontendStoredFile> => {
        try {
          const newDoc = await fileSystemStorage.createDocumentInWorkspace(name, parentId);
          await get().refreshFiles();

          // Automatically open the newly created document in a new tab
          await get().openTab(newDoc);

          return newDoc;
        } catch (error) {
          console.error('Error creating document:', error);
          throw error;
        }
      },

      deleteFile: async (id: string): Promise<{ success: boolean; deletedIds: string[] }> => {
        const { openFiles, refreshFiles } = get();

        try {
          const result = await fileSystemStorage.deleteFile(id);
          if (result.success) {
            // Remove deleted files from openFiles
            set((state) => {
              state.openFiles = state.openFiles.filter(f => !result.deletedIds.includes(f.id));
            });

            await refreshFiles();
          }
          return result;
        } catch (error) {
          console.error('Error deleting file:', error);
          throw error;
        }
      },

      renameFile: async (fileId: string, newName: string): Promise<void> => {
        const { detailedActiveFile, setDetailedActiveFile, refreshFiles, loadFile } = get();

        try {
          await fileSystemStorage.renameFile(fileId, newName);

          // If the renamed file is the active one, refresh its details
          if (detailedActiveFile && detailedActiveFile.id === fileId) {
            const updatedFileDetails = await loadFile(fileId);
            if (updatedFileDetails) {
              setDetailedActiveFile(updatedFileDetails);
            }
          }

          await refreshFiles();
        } catch (error) {
          console.error('Error renaming file:', error);
          throw error;
        }
      },

      duplicateFile: async (fileIdToDuplicate: string, suggestedNewName: string): Promise<FrontendStoredFile> => {
        try {
          const newDoc = await fileSystemStorage.duplicateFile(fileIdToDuplicate, suggestedNewName);
          await get().refreshFiles();
          return newDoc;
        } catch (error) {
          console.error('Error duplicating file:', error);
          throw error;
        }
      },

      createFolder: async (folderName: string, parentId?: string | null): Promise<FrontendStoredFile> => {
        try {
          const newFolder = await fileSystemStorage.createFolder(folderName, parentId);
          await get().refreshFiles();
          return newFolder;
        } catch (error) {
          console.error('Error creating folder:', error);
          throw error;
        }
      },

      exportFile: async (fileToExport: FrontendStoredFile): Promise<boolean> => {
        try {
          return await fileSystemStorage.exportToDisk(fileToExport);
        } catch (error) {
          console.error('Error exporting file:', error);
          throw error;
        }
      },

      moveItem: async (itemId: string, newParentId: string | null): Promise<{ movedItem: FrontendStoredFile; updatedChildren?: FrontendStoredFile[] }> => {
        try {
          const result = await fileSystemStorage.moveItem(itemId, newParentId);
          await get().refreshFiles();
          return result;
        } catch (error) {
          console.error('Error moving item:', error);
          throw error;
        }
      },

      // Project settings
      loadProjectSettings: async () => {
        try {
          const settings = await fileSystemStorage.loadProjectSettings();
          set((state) => {
            state.projectSettings = settings;
          });
        } catch (error) {
          console.error('Error loading project settings:', error);
          throw error;
        }
      },

      saveProjectSettings: async (settings: ProjectSettings) => {
        try {
          await fileSystemStorage.saveProjectSettings(settings);
          set((state) => {
            state.projectSettings = settings;
          });
        } catch (error) {
          console.error('Error saving project settings:', error);
          throw error;
        }
      },

      // Block-level operations
      loadDocumentBlocks: async (documentId: string) => {
        try {
          if (typeof window !== 'undefined' && window.fileStorage) {
            const blockResults = await window.fileStorage.loadDocumentBlocks(documentId);
            const metadata = await window.fileStorage.loadDocumentBlocksMetadata(documentId);

            set((state) => {
              // Clear existing blocks for this document
              const existingBlockIds = Array.from(state.blocks.keys()).filter(blockId =>
                state.blocks.get(blockId)?.content?.documentId === documentId
              );
              existingBlockIds.forEach(blockId => state.blocks.delete(blockId));

              // Add loaded blocks
              blockResults.forEach(result => {
                if (result.success && result.content) {
                  state.blocks.set(result.blockId, {
                    content: result.content,
                    lastModified: new Date().toISOString(),
                    isLoaded: true,
                    isDirty: false,
                    saveCount: 0
                  });
                }
              });

              // Add metadata
              if (metadata) {
                state.documentsMetadata.set(documentId, metadata);
              }

              // Mark document as loaded
              state.loadedDocuments.add(documentId);
            });
          } else {
            console.warn('window.fileStorage not available for loadDocumentBlocks');
          }
        } catch (error) {
          console.error(`Error loading blocks for document ${documentId}:`, error);
          throw error;
        }
      },

      updateBlock: (blockId: string, content: any) => set((state) => {
        const existing = state.blocks.get(blockId);
        if (existing) {
          existing.content = content;
          existing.lastModified = new Date().toISOString();
          existing.isDirty = true;
        } else {
          state.blocks.set(blockId, {
            content,
            lastModified: new Date().toISOString(),
            isLoaded: true,
            isDirty: true,
            saveCount: 0
          });
        }
        state.dirtyBlocks.add(blockId);
      }),

      // Centralized action to update document state with authoritative blockOrder from editor
      updateActiveDocumentState: (docId: string, editorBlocks: any[], newBlockOrder: string[]) => set((state) => {
        console.log(`[Store] Updating active document state for ${docId} with ${editorBlocks.length} blocks`);

        const now = new Date().toISOString();

        // Update each block in the store
        editorBlocks.forEach((block: any) => {
          if (block && block.id) {
            const blockWithDocId = {
              ...block,
              documentId: docId
            };

            const existing = state.blocks.get(block.id);
            if (existing) {
              existing.content = blockWithDocId;
              existing.lastModified = now;
              existing.isDirty = true;
            } else {
              state.blocks.set(block.id, {
                content: blockWithDocId,
                lastModified: now,
                isLoaded: true,
                isDirty: true,
                saveCount: 0
              });
            }
            state.dirtyBlocks.add(block.id);
          }
        });

        // Update document metadata with authoritative blockOrder
        const existingMetadata = state.documentsMetadata.get(docId);
        const updatedMetadata = {
          version: existingMetadata?.version || '1.0',
          documentId: docId,
          blockOrder: newBlockOrder,
          lastModified: now,
          totalBlocks: newBlockOrder.length
        };

        state.documentsMetadata.set(docId, updatedMetadata);
        console.log(`[Store] Updated metadata for ${docId} with blockOrder:`, newBlockOrder);
      }),

      createBlock: (blockId: string, content: any) => set((state) => {
        state.blocks.set(blockId, {
          content,
          lastModified: new Date().toISOString(),
          isLoaded: true,
          isDirty: true,
          saveCount: 0
        });
        state.dirtyBlocks.add(blockId);
      }),

      deleteBlock: (blockId: string) => set((state) => {
        state.blocks.delete(blockId);
        state.dirtyBlocks.delete(blockId);
        state.blockSaveQueue.delete(blockId);
      }),

      markBlockClean: (blockId: string) => set((state) => {
        const block = state.blocks.get(blockId);
        if (block) {
          block.isDirty = false;
          block.saveCount += 1;
        }
        state.dirtyBlocks.delete(blockId);
      }),

      markBlockDirty: (blockId: string) => set((state) => {
        const block = state.blocks.get(blockId);
        if (block) {
          block.isDirty = true;
        }
        state.dirtyBlocks.add(blockId);
      }),

      // Method to update document metadata from auto-save manager
      updateDocumentMetadata: (documentId: string, metadata: any) => set((state) => {
        state.documentsMetadata.set(documentId, metadata);
      }),

      getBlock: (blockId: string): BlockState | null => {
        return get().blocks.get(blockId) || null;
      },

      getDocumentBlocks: (documentId: string): BlockState[] => {
        const { blocks, documentsMetadata } = get();
        const metadata = documentsMetadata.get(documentId);

        if (!metadata) return [];

        return metadata.blockOrder
          .map(blockId => blocks.get(blockId))
          .filter((block): block is BlockState => block !== undefined);
      },

      getDirtyBlocks: (): string[] => {
        return Array.from(get().dirtyBlocks);
      },

      queueBlockForSave: (blockId: string, content: any, operation: 'create' | 'update' | 'delete') => set((state) => {
        state.blockSaveQueue.set(blockId, {
          content,
          operation,
          timestamp: Date.now()
        });
      }),

      clearSaveQueue: () => set((state) => {
        state.blockSaveQueue.clear();
      }),

      getSaveQueue: () => {
        return new Map(get().blockSaveQueue);
      },

      setAutoSaving: (saving: boolean) => set((state) => {
        state.isAutoSaving = saving;
      }),

      updateLastAutoSaveTime: () => set((state) => {
        state.lastAutoSaveTime = Date.now();
      }),

      migrateDocumentToBlocks: async (documentId: string) => {
        try {
          if (typeof window !== 'undefined' && window.fileStorage) {
            const result = await window.fileStorage.migrateDocumentToBlocks(documentId);
            if (result) {
              // Reload the document blocks after migration
              await get().loadDocumentBlocks(documentId);
            }
          }
        } catch (error) {
          console.error(`Error migrating document ${documentId} to blocks:`, error);
          throw error;
        }
      },

      isDocumentBlockBased: async (documentId: string): Promise<boolean> => {
        try {
          if (typeof window !== 'undefined' && window.fileStorage) {
            return await window.fileStorage.isDocumentBlockBased(documentId);
          }
          return false;
        } catch (error) {
          console.error(`Error checking if document ${documentId} is block-based:`, error);
          return false;
        }
      },

      // Pending insertion content actions
      setPendingInsertionContent: (content: any[] | null) => set((state) => {
        state.pendingInsertionContent = content;
      })
    }))
      ),
      {
        name: 'document-storage',
        storage: createJSONStorage(() => localStorage),
        partialize: (state) => ({
          // Only persist these fields
          currentStoragePath: state.currentStoragePath
          // Note: openFiles and activeFileId are intentionally not persisted
          // so that tabs start empty on each app load
        }),
      }
    ),
    {
      name: 'document-store'
    }
  )
);

// Selectors for optimized access
export const selectActiveDocument = (state: BlockDocumentState) => state.detailedActiveFile;
export const selectDirtyBlocks = (state: BlockDocumentState) => Array.from(state.dirtyBlocks);
export const selectIsAutoSaving = (state: BlockDocumentState) => state.isAutoSaving;
export const selectBlockById = (blockId: string) => (state: BlockDocumentState) => state.blocks.get(blockId);
export const selectDocumentBlocks = (documentId: string) => (state: BlockDocumentState) => {
  const metadata = state.documentsMetadata.get(documentId);
  if (!metadata) return [];

  return metadata.blockOrder
    .map(blockId => ({ blockId, ...state.blocks.get(blockId) }))
    .filter(block => block.content !== undefined);
};

// Optimized typed selectors with memoization for better performance
export const useActiveDocument = () =>
  useDocumentStore(
    useCallback((state) => state.detailedActiveFile, [])
  );

export const useProjectSettings = () =>
  useDocumentStore(
    useCallback((state) => state.projectSettings, [])
  );

export const useOpenFiles = () =>
  useDocumentStore(
    useCallback((state) => state.openFiles, [])
  );

export const useActiveFileId = () =>
  useDocumentStore(
    useCallback((state) => state.activeFileId, [])
  );

export const useFiles = () =>
  useDocumentStore(
    useCallback((state) => state.files, [])
  );

export const useCurrentStoragePath = () =>
  useDocumentStore(
    useCallback((state) => state.currentStoragePath, [])
  );

export const useEditorMode = () =>
  useDocumentStore(
    useCallback((state) => state.editorMode, [])
  );

// Selector for document operations
export const useDocumentOperations = () =>
  useDocumentStore(state => ({
    openTab: state.openTab,
    closeTab: state.closeTab,
    selectTab: state.selectTab,
    reorderTabs: state.reorderTabs,
    loadFile: state.loadFile,
    saveDocumentContent: state.saveDocumentContent,
    saveDocumentMetadata: state.saveDocumentMetadata
  }));

// Selector for file operations
export const useFileOperations = () =>
  useDocumentStore(state => ({
    createDocument: state.createDocument,
    createFolder: state.createFolder,
    deleteFile: state.deleteFile,
    renameFile: state.renameFile,
    moveItem: state.moveItem,
    refreshFiles: state.refreshFiles
  }));

// Selector for settings operations
export const useSettingsOperations = () =>
  useDocumentStore(state => ({
    loadProjectSettings: state.loadProjectSettings,
    saveProjectSettings: state.saveProjectSettings,
    selectStorageDirectory: state.selectStorageDirectory,
    unloadWorkspace: state.unloadWorkspace,
    setProjectSettings: state.setProjectSettings,
    setCurrentStoragePath: state.setCurrentStoragePath
  }));

// Selector for pending insertion content
export const usePendingInsertionContent = () =>
  useDocumentStore(state => state.pendingInsertionContent);

// Selector for pending insertion actions
export const usePendingInsertionActions = () =>
  useDocumentStore(state => ({
    setPendingInsertionContent: state.setPendingInsertionContent
  }));

// Block-specific selectors
export const useDocumentBlocks = (documentId: string | null) =>
  useDocumentStore(state => documentId ? state.getDocumentBlocks(documentId) : []);

// Keep the original hook name for backward compatibility during migration
export const useBlockDocumentStore = useDocumentStore;
